<template>
  <div class="subscription-management">
    <div class="header">
      <h2>Subscription Management</h2>
      <div class="header-actions">
        <el-button
          @click="toggleSearch"
          :icon="searchExpanded ? ArrowUp : ArrowDown"
          type="default"
          class="search-toggle-btn"
          :aria-expanded="searchExpanded"
          :aria-controls="'search-form-container'"
          :title="searchExpanded ? 'Hide search filters' : 'Show search filters'"
        >
          <span class="search-toggle-text">
            {{ searchExpanded ? 'Hide Search' : 'Show Search' }}
          </span>
          <el-badge
            v-if="activeFiltersCount > 0"
            :value="activeFiltersCount"
            class="filter-badge"
            type="primary"
          />
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          Add Subscription
        </el-button>
      </div>
    </div>

    <!-- Collapsible Search Form -->
    <el-collapse-transition>
      <div
        v-show="searchExpanded"
        class="search-container"
        id="search-form-container"
        role="region"
        aria-label="Search and filter options"
      >
        <el-form :model="searchForm" class="search-form" :inline="true">
          <el-form-item label="ID">
            <el-input
              v-model="searchForm.id"
              placeholder="Search by exact ID"
              clearable
              style="width: 120px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="Token ID">
            <el-input
              v-model="searchForm.tokenId"
              placeholder="Search by exact Token ID"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="Email">
            <el-input
              v-model="searchForm.email"
              placeholder="Search by email (partial match)"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="Valid Until">
            <el-date-picker
              v-model="searchForm.validUntilStart"
              type="date"
              placeholder="Start date"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
            <span style="margin: 0 8px;">to</span>
            <el-date-picker
              v-model="searchForm.validUntilEnd"
              type="date"
              placeholder="End date"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
          </el-form-item>
          <el-form-item label="Next Reset">
            <el-date-picker
              v-model="searchForm.nextResetStart"
              type="date"
              placeholder="Start date"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
            <span style="margin: 0 8px;">to</span>
            <el-date-picker
              v-model="searchForm.nextResetEnd"
              type="date"
              placeholder="End date"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
          </el-form-item>
          <el-form-item label="Lines">
            <el-select
              v-model="searchForm.lines"
              placeholder="Select lines"
              clearable
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 200px"
              @change="handleSearchChange"
              :filter-method="filterSearchLines"
              default-first-option
              reserve-keyword
              :loading="loadingAllServers"
            >
              <el-option
                v-for="line in filteredSearchLines"
                :key="line.id"
                :label="line.display_name"
                :value="line.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              Search
            </el-button>
            <el-button @click="handleClearSearch">
              Clear
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <el-card class="table-card">
      <el-table
        :data="subscriptions"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        class="subscription-table"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          prop="token_id"
          label="Token ID"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              class="token-id-cell"
              @click="handleCopyTokenId(scope.row)"
              :title="'Click to copy: ' + scope.row.token_id"
            >
              {{ scope.row.token_id }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="email_address"
          label="Email"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="valid_until"
          label="Valid Until"
          width="160"
          :formatter="formatDate"
          align="center"
        />
        <el-table-column
          prop="next_reset"
          label="Next Reset"
          width="160"
          :formatter="formatDate"
          align="center"
        />
        <el-table-column
          label="Traffic"
          min-width="300"
          align="center"
        >
          <template #default="scope">
            <div class="traffic-progress">
              <div class="progress-wrapper">
                <div 
                  class="progress-bar" 
                  :style="{ width: `${getTrafficPercentage(scope.row)}%` }"
                  :class="{ 'zero-progress': getTrafficPercentage(scope.row) === 0 }"
                >
                </div>
                <span class="progress-text">
                  {{ formatTraffic(null, null, scope.row.traffic_used) }} / {{ formatTraffic(null, null, scope.row.traffic_total) }} ({{ getTrafficPercentage(scope.row).toFixed(1) }}%)
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="lines"
          label="Lines"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-popover
              placement="top"
              :width="200"
              trigger="hover"
              popper-class="line-popover"
            >
              <template #reference>
                <el-tag
                  type="info"
                  class="line-count"
                  effect="plain"
                >
                  {{ scope.row.lines.length }} Lines
                </el-tag>
              </template>
              <template #default>
                <div class="line-list">
                  <div class="line-list-header">Line Details</div>
                  <el-scrollbar max-height="200px">
                    <div 
                      v-for="line in scope.row.lines" 
                      :key="line.id"
                      class="line-item"
                    >
                      <div class="line-name">{{ line.display_name || 'Unnamed Line' }}</div>
                      <el-tag 
                        size="small" 
                        :type="line.is_online ? 'success' : 'danger'"
                      >
                        {{ line.is_online ? 'Active' : 'Inactive' }}
                      </el-tag>
                    </div>
                  </el-scrollbar>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          label="Actions"
          width="180"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <el-space>
              <el-button
                size="small"
                type="primary"
                @click="handleExtend(scope.row)"
                :icon="Timer"
                circle
              />
              <el-button
                size="small"
                type="warning"
                @click="handleResetTraffic(scope.row)"
                :icon="Refresh"
                circle
              />
              <el-button
                size="small"
                type="info"
                @click="handleEdit(scope.row)"
                circle
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.row)"
                :icon="Delete"
                circle
              />
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination Controls -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>Total {{ totalItems }} items</span>
          <el-select
            v-model="pageSize"
            @change="handlePageSizeChange"
            class="page-size-selector"
            size="small"
          >
            <el-option
              v-for="size in pageSizeOptions"
              :key="size"
              :label="`${size} / page`"
              :value="size"
            />
          </el-select>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalItems"
          :page-count="totalPages"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          class="pagination-controls"
          small
        />
      </div>
    </el-card>

    <!-- Add User Dialog -->
    <el-dialog
      v-model="showAddDialog"
      title="Add New Subscription"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="180px"
        label-position="left"
      >
        <el-form-item label="Email Address" prop="email_address">
          <el-input v-model="form.email_address" placeholder="Enter email address" />
        </el-form-item>

        <el-form-item label="Bandwidth (Mbps)" prop="bandwidth">
          <el-input-number 
            v-model="form.bandwidth" 
            :min="0"
            placeholder="Leave empty for unlimited"
          />
        </el-form-item>

        <el-form-item label="Traffic (GB)" prop="traffic">
          <el-input-number 
            v-model="trafficGB" 
            :min="1"
            :precision="0"
            placeholder="Enter traffic limit in GB"
          />
        </el-form-item>

        <el-form-item label="Max Ports Per Server" prop="max_ports_per_server">
          <el-input-number 
            v-model="form.max_ports_per_server" 
            :min="1"
            :precision="0"
          />
        </el-form-item>

        <el-form-item label="Billing Type" prop="bill_type">
          <el-radio-group v-model="billingType">
            <el-radio label="cycle">Cycle</el-radio>
            <el-radio label="onetime">One Time</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="billingType === 'cycle'">
          <el-form-item label="Cycle Days" prop="cycle_days">
            <el-input-number 
              v-model="form.cycle_days" 
              :min="1"
              :precision="0"
            />
          </el-form-item>
          <el-form-item label="Cycle Price" prop="cycle_price">
            <el-input-number 
              v-model="form.cycle_price" 
              :min="0"
              :precision="0"
            />
          </el-form-item>
        </template>

        <template v-else>
          <el-form-item label="Total Days" prop="total_days">
            <el-input-number 
              v-model="form.total_days" 
              :min="1"
              :precision="0"
            />
          </el-form-item>
          <el-form-item label="One Time Price" prop="onetime_price">
            <el-input-number 
              v-model="form.onetime_price" 
              :min="0"
              :precision="0"
            />
          </el-form-item>
        </template>

        <el-form-item label="Lines" prop="lines">
          <el-select
            v-model="form.lines"
            multiple
            filterable
            remote
            :remote-method="searchServers"
            :loading="loadingAllServers"
            placeholder="Search and select lines"
            style="width: 100%"
          >
            <el-option
              v-for="line in filteredDialogLines"
              :key="line.id"
              :label="line.display_name"
              :value="line.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.activated">Activate Immediately</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.allow_forward_endpoint">Allow Forward Endpoint</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">Cancel</el-button>
          <el-button type="primary" @click="handleAddSubmit" :loading="submitting">
            Create
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Edit User Dialog -->
    <el-dialog
      v-model="showEditDialog"
      title="Edit Subscription"
      width="600px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="rules"
        label-width="180px"
        label-position="left"
      >
        <el-form-item label="Email Address" prop="email_address">
          <el-input v-model="editForm.email_address" placeholder="Enter email address" />
        </el-form-item>

        <el-form-item label="Bandwidth (Mbps)" prop="bandwidth">
          <el-input-number
            v-model="editForm.bandwidth"
            :min="0"
            placeholder="Leave empty for unlimited"
          />
        </el-form-item>

        <el-form-item label="Traffic (GB)" prop="traffic">
          <el-input-number
            v-model="editTrafficGB"
            :min="1"
            :precision="0"
            placeholder="Enter traffic limit in GB"
          />
        </el-form-item>

        <el-form-item label="Max Ports per Server" prop="max_ports_per_server">
          <el-input-number
            v-model="editForm.max_ports_per_server"
            :min="1"
            :max="100"
          />
        </el-form-item>

        <el-form-item label="Billing Type">
          <el-radio-group v-model="editBillingType">
            <el-radio label="cycle">Cycle Billing</el-radio>
            <el-radio label="onetime">One-time Billing</el-radio>
          </el-radio-group>
        </el-form-item>

        <div v-if="editBillingType === 'cycle'">
          <el-form-item label="Cycle Days" prop="cycle_days">
            <el-input-number
              v-model="editForm.cycle_days"
              :min="1"
              :max="365"
            />
          </el-form-item>

          <el-form-item label="Cycle Price" prop="cycle_price">
            <el-input-number
              v-model="editForm.cycle_price"
              :min="0"
              :precision="2"
            />
          </el-form-item>
        </div>

        <div v-else>
          <el-form-item label="One-time Price" prop="onetime_price">
            <el-input-number
              v-model="editForm.onetime_price"
              :min="0"
              :precision="2"
            />
          </el-form-item>

          <el-form-item label="Total Days" prop="total_days">
            <el-input-number
              v-model="editForm.total_days"
              :min="1"
              :max="3650"
            />
          </el-form-item>
        </div>

        <el-form-item label="Lines" prop="lines">
          <el-select
            v-model="editForm.lines"
            multiple
            filterable
            remote
            :remote-method="searchServers"
            placeholder="Search and select lines"
            style="width: 100%"
            :loading="loadingAllServers"
          >
            <el-option
              v-for="line in filteredDialogLines"
              :key="line.id"
              :label="line.display_name"
              :value="line.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="editForm.activated">Activate Immediately</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="editForm.allow_forward_endpoint">Allow Forward Endpoint</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">Cancel</el-button>
          <el-button type="primary" @click="handleEditSubmit" :loading="submitting">
            Update
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getSubscriptionList, addUser, editUser, removeUser, extendSubscriptionTime, resetUserTraffic, getServerList, getAllServerList } from '../api'
import dayjs from 'dayjs'
import { Timer, Delete, Refresh, Edit, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { useThemeStore } from '../stores/theme'

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Computed style for table headers - borderless design
const headerCellStyle = computed(() => ({
  background: isDark.value ? 'var(--theme-fill-dark)' : 'var(--theme-fill-light)',
  color: 'var(--theme-text-primary)',
  fontWeight: '600',
  border: 'none'
}))

const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const formRef = ref(null)
const editFormRef = ref(null)
const subscriptions = ref([])

// Pagination state
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const totalPages = ref(1)
const pageSizeOptions = [20, 50, 100, 200]

// Search state
const searchForm = ref({
  id: '',
  tokenId: '',
  email: '',
  validUntilStart: '',
  validUntilEnd: '',
  nextResetStart: '',
  nextResetEnd: '',
  lines: []
})
const searchTimeout = ref(null)

// Search UI state
const searchExpanded = ref(false)

const billingType = ref('cycle')
const editBillingType = ref('cycle')
const trafficGB = ref(100)
const editTrafficGB = ref(100)
const editingUser = ref(null)

const availableLines = ref([])
const loadingServers = ref(false)

// Global data for search dropdowns
const allAvailableLines = ref([])
const loadingAllServers = ref(false)

// Load all servers for search dropdown (admin only)
const loadAllServersForSearch = async () => {
  loadingAllServers.value = true
  try {
    const response = await getAllServerList()
    const serverList = response.data?.servers || []
    if (Array.isArray(serverList)) {
      allAvailableLines.value = serverList.map(server => ({
        id: server.id,
        display_name: server.display_name || `Server ${server.id}`
      }))
      console.log('Successfully loaded all servers for search:', allAvailableLines.value.length, 'items')
      // Initialize filtered search lines and dialog lines
      filteredSearchLines.value = allAvailableLines.value
      filteredDialogLines.value = allAvailableLines.value
    } else {
      throw new Error('Invalid server list format')
    }
  } catch (error) {
    ElMessage.error('Failed to load complete server list for search')
    console.error('Failed to load complete server list:', error)
    allAvailableLines.value = []
    filteredSearchLines.value = []
    filteredDialogLines.value = []
  } finally {
    loadingAllServers.value = false
  }
}

// Load server list from API (for form dropdowns)
const loadServerList = async () => {
  loadingServers.value = true
  try {
    const response = await getServerList()
    const serverList = response.data?.servers || []
    if (Array.isArray(serverList)) {
      availableLines.value = serverList.map(server => ({
        id: server.id,
        display_name: server.display_name || `Server ${server.id}`
      }))
    } else {
      throw new Error('Invalid server list format')
    }
  } catch (error) {
    ElMessage.error('Failed to load server list')
    console.error('Failed to load server list:', error)
  } finally {
    loadingServers.value = false
  }
}

const form = ref({
  email_address: '',
  bandwidth: null,
  traffic: 0,
  activated: true,
  max_ports_per_server: 1,
  cycle_days: 30,
  cycle_price: 0,
  onetime_price: 0,
  total_days: 365,
  lines: [],
  allow_forward_endpoint: false
})

const editForm = ref({
  user_id: null,
  email_address: '',
  bandwidth: null,
  traffic: 0,
  activated: true,
  max_ports_per_server: 1,
  cycle_days: 30,
  cycle_price: 0,
  onetime_price: 0,
  total_days: 365,
  lines: [],
  allow_forward_endpoint: false
})

const rules = {
  email_address: [
    { required: true, message: 'Please enter email address', trigger: 'blur' },
    { type: 'email', message: 'Please enter valid email address', trigger: 'blur' }
  ],
  max_ports_per_server: [
    { required: true, message: 'Please enter max ports per server', trigger: 'blur' }
  ],
  lines: [
    { required: true, message: 'Please select at least one line', trigger: 'change' }
  ]
}

// Watch traffic GB and convert to bytes
watch(trafficGB, (newValue) => {
  form.value.traffic = newValue
})

watch(editTrafficGB, (newValue) => {
  editForm.value.traffic = newValue
})

const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    email_address: '',
    bandwidth: null,
    traffic: 0,
    activated: true,
    max_ports_per_server: 1,
    cycle_days: 30,
    cycle_price: 0,
    onetime_price: 0,
    total_days: 365,
    lines: [],
    allow_forward_endpoint: false
  }
  trafficGB.value = 100
  billingType.value = 'cycle'
}

const resetEditForm = () => {
  editFormRef.value?.resetFields()
  editForm.value = {
    user_id: null,
    email_address: '',
    bandwidth: null,
    traffic: 0,
    activated: false, // Default to false for consistency
    max_ports_per_server: 1,
    cycle_days: 30,
    cycle_price: 0,
    onetime_price: 0,
    total_days: 365,
    lines: [],
    allow_forward_endpoint: false // Explicitly set to false
  }
  editTrafficGB.value = 100
  editBillingType.value = 'cycle'
}

const handleAddSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const submitData = {
          address: form.value.email_address,
          bandwidth: form.value.bandwidth,
          traffic: trafficGB.value,
          activated: form.value.activated,
          max_ports_per_server: form.value.max_ports_per_server,
          bill_type: billingType.value === 'cycle'
            ? { Cycle: { days: form.value.cycle_days, price: form.value.cycle_price } }
            : { OneTime: { price: form.value.onetime_price, days: form.value.total_days } },
          total_days: billingType.value === 'cycle' ? form.value.cycle_days : form.value.total_days,
          lines: form.value.lines,
          allow_forward_endpoint: form.value.allow_forward_endpoint
        }

        await addUser(submitData)
        ElMessage.success('Subscription added successfully')
        showAddDialog.value = false
        resetForm()
        loadSubscriptions(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Failed to add subscription:', error)
        ElMessage.error('Failed to add subscription')
      } finally {
        submitting.value = false
      }
    }
  })
}

const formatDate = (row, column, cellValue) => {
  if (!cellValue) return '-'
  return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
}

const formatTraffic = (row, column, cellValue) => {
  if (cellValue === 0) return '0 GB'
  const GB = 1024 * 1024 * 1024
  return `${(cellValue / GB).toFixed(2)} GB`
}

const getTrafficPercentage = (row) => {
  if (!row.traffic_total || !row.traffic_used) return 0
  const percentage = (row.traffic_used / row.traffic_total) * 100
  return Math.min(percentage, 100) // 确保不超过100%
}

const handleAdd = () => {
  // TODO: Implement add functionality when backend API is ready
  console.log('Add new subscription')
}

const handleEdit = (row) => {
  console.log('Edit:', row)
  console.log('Row activated field:', row.activated)
  console.log('Row allow_forward_endpoint field:', row.allow_forward_endpoint)
  editingUser.value = row

  // Ensure allAvailableLines includes all currently assigned lines
  if (row.lines && Array.isArray(row.lines)) {
    const currentLineIds = new Set(allAvailableLines.value.map(line => line.id))
    const missingLines = row.lines.filter(line => !currentLineIds.has(line.id))

    if (missingLines.length > 0) {
      // Add missing lines to allAvailableLines so they can be displayed properly
      allAvailableLines.value = [...allAvailableLines.value, ...missingLines.map(line => ({
        id: line.id,
        display_name: line.display_name || `Server ${line.id}`
      }))]
      // Update filtered lists as well
      filteredSearchLines.value = allAvailableLines.value
      filteredDialogLines.value = allAvailableLines.value
    }
  }

  // Pre-populate the edit form with existing data
  // Extract billing information from the bill_type object
  let cycle_days = 30
  let cycle_price = 0
  let onetime_price = 0
  let billing_type = 'cycle'

  if (row.bill_type) {
    if (row.bill_type.Cycle) {
      billing_type = 'cycle'
      cycle_days = row.bill_type.Cycle.days || 30
      cycle_price = row.bill_type.Cycle.price || 0
    } else if (row.bill_type.OneTime) {
      billing_type = 'onetime'
      onetime_price = row.bill_type.OneTime.price || 0
      cycle_days = row.bill_type.OneTime.days || 365
    }
  }

  editForm.value = {
    user_id: row.id,
    email_address: row.email_address || row.owner_address || '',
    bandwidth: row.bandwidth,
    traffic: row.traffic_total,
    activated: Boolean(row.activated), // Ensure boolean value
    max_ports_per_server: row.max_ports_per_server,
    cycle_days: cycle_days,
    cycle_price: cycle_price,
    onetime_price: onetime_price,
    total_days: row.total_days || cycle_days,
    lines: row.lines ? row.lines.map(line => line.id) : [],
    allow_forward_endpoint: Boolean(row.allow_forward_endpoint) // Ensure boolean value
  }

  // Set traffic in GB for the input
  const GB = 1024 * 1024 * 1024
  editTrafficGB.value = Math.round(row.traffic_total / GB)

  // Set billing type based on the bill_type object
  editBillingType.value = billing_type

  showEditDialog.value = true
}

const handleEditSubmit = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const submitData = {
          user_id: editForm.value.user_id,
          address: editForm.value.email_address,
          bandwidth: editForm.value.bandwidth,
          traffic: editTrafficGB.value,
          activated: Boolean(editForm.value.activated), // Ensure boolean value is always sent
          max_ports_per_server: editForm.value.max_ports_per_server,
          bill_type: editBillingType.value === 'cycle'
            ? { Cycle: { days: editForm.value.cycle_days, price: editForm.value.cycle_price } }
            : { OneTime: { price: editForm.value.onetime_price, days: editForm.value.total_days } },
          total_days: editBillingType.value === 'cycle' ? editForm.value.cycle_days : editForm.value.total_days,
          lines: editForm.value.lines,
          allow_forward_endpoint: Boolean(editForm.value.allow_forward_endpoint) // Ensure boolean value is always sent
        }

        console.log('Submitting edit data:', submitData) // Debug log to verify data structure
        await editUser(submitData)
        ElMessage.success('Subscription updated successfully')
        showEditDialog.value = false
        resetEditForm()
        loadSubscriptions(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Failed to update subscription:', error)
        ElMessage.error('Failed to update subscription')
      } finally {
        submitting.value = false
      }
    }
  })
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      'This will permanently delete the subscription. Continue?',
      'Warning',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    loading.value = true
    await removeUser(row.id)
    ElMessage.success('Subscription deleted successfully')
    await loadSubscriptions(currentPage.value, pageSize.value)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('Failed to delete subscription')
    }
  } finally {
    loading.value = false
  }
}

const handleExtend = async (row) => {
  try {
    const { value: days } = await ElMessageBox.prompt(
      'Enter number of days to extend (leave empty to use subscription cycle)',
      'Extend Subscription',
      {
        confirmButtonText: 'Extend',
        cancelButtonText: 'Cancel',
        inputPattern: /^[0-9]*$/,
        inputErrorMessage: 'Please enter a valid number',
        inputPlaceholder: 'Enter days or leave empty',
        showClose: false
      }
    )
    
    loading.value = true
    const daysNumber = days === '' ? null : parseInt(days)
    await extendSubscriptionTime(row.id, daysNumber)
    ElMessage.success('Subscription extended successfully')
    await loadSubscriptions(currentPage.value, pageSize.value)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('Failed to extend subscription')
    }
  } finally {
    loading.value = false
  }
}

const handleResetTraffic = (row) => {
  ElMessageBox.confirm(
    'Are you sure to reset traffic for this subscription? This will clear all traffic usage data.',
    'Warning',
    {
      confirmButtonText: 'Reset',
      cancelButtonText: 'Cancel',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await resetUserTraffic(row.id)
      ElMessage.success('Traffic reset successfully')
      loadSubscriptions(currentPage.value, pageSize.value)  // 重新加载列表以更新数据
    } catch (error) {
      console.error('Error resetting traffic:', error)
      ElMessage.error(error.message || 'Failed to reset traffic')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

const handleCopyTokenId = async (row) => {
  const tokenId = row.token_id
  const email = row.email_address || row.owner_address || 'Unknown'
  const subscriptionId = row.id

  try {
    // Try modern Clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(tokenId)
    } else {
      // Fallback for older browsers or non-secure contexts
      const textArea = document.createElement('textarea')
      textArea.value = tokenId
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        textArea.remove()
      } catch (err) {
        console.error('Failed to copy text:', err)
        textArea.remove()
        throw new Error('Failed to copy text')
      }
    }

    // Show success notification with detailed information
    ElMessage.success(`Token ID ${tokenId} copied to clipboard for ${email} (ID: ${subscriptionId})`)
  } catch (err) {
    console.error('Failed to copy token ID:', err)
    ElMessage.error('Failed to copy token ID to clipboard')
  }
}

const loadSubscriptions = async (page = currentPage.value, size = pageSize.value) => {
  loading.value = true
  try {
    // Prepare search parameters
    const searchParams = {
      page,
      page_size: size
    }

    // Add search criteria if they exist
    if (searchForm.value.id?.trim()) {
      searchParams.id = parseInt(searchForm.value.id.trim())
    }
    if (searchForm.value.tokenId?.trim()) {
      searchParams.token_id = searchForm.value.tokenId.trim()
    }
    if (searchForm.value.email?.trim()) {
      searchParams.email = searchForm.value.email.trim()
    }
    if (searchForm.value.validUntilStart) {
      searchParams.valid_until_start = dayjs(searchForm.value.validUntilStart).format('YYYY-MM-DD')
    }
    if (searchForm.value.validUntilEnd) {
      searchParams.valid_until_end = dayjs(searchForm.value.validUntilEnd).format('YYYY-MM-DD')
    }
    if (searchForm.value.nextResetStart) {
      searchParams.next_reset_start = dayjs(searchForm.value.nextResetStart).format('YYYY-MM-DD')
    }
    if (searchForm.value.nextResetEnd) {
      searchParams.next_reset_end = dayjs(searchForm.value.nextResetEnd).format('YYYY-MM-DD')
    }
    if (searchForm.value.lines?.length > 0) {
      searchParams.lines = searchForm.value.lines
    }

    const { data } = await getSubscriptionList(searchParams)

    // Handle paginated response
    if (data.subscriptions && data.pagination) {
      // New paginated response format
      subscriptions.value = data.subscriptions
      currentPage.value = data.pagination.current_page
      pageSize.value = data.pagination.page_size
      totalItems.value = data.pagination.total_items
      totalPages.value = data.pagination.total_pages
    } else if (data?.subscriptions) {
      // Fallback for old response format (if backend doesn't support pagination yet)
      subscriptions.value = data.subscriptions
      totalItems.value = data.subscriptions.length
      totalPages.value = 1
    } else {
      subscriptions.value = []
      totalItems.value = 0
      totalPages.value = 1
    }
  } catch (error) {
    ElMessage.error('Failed to load subscriptions')
    subscriptions.value = []
    totalItems.value = 0
    totalPages.value = 1
  } finally {
    loading.value = false
  }
}

const searchServers = (query) => {
  if (query) {
    filteredDialogLines.value = allAvailableLines.value.filter(line =>
      line.display_name.toLowerCase().includes(query.toLowerCase())
    )
  } else {
    filteredDialogLines.value = allAvailableLines.value
  }
}

// Pagination handlers
const handlePageChange = (page) => {
  currentPage.value = page
  loadSubscriptions(page, pageSize.value)
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // Reset to first page when changing page size
  loadSubscriptions(1, size)
}

// Search handlers
const handleSearchInput = () => {
  // Debounce search input to avoid too many API calls
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  searchTimeout.value = setTimeout(() => {
    handleSearch()
  }, 500)
}

const handleSearchChange = () => {
  // Immediate search for dropdown and date picker changes
  handleSearch()
}

const handleSearch = () => {
  currentPage.value = 1 // Reset to first page when searching
  loadSubscriptions(1, pageSize.value)
}

const handleClearSearch = () => {
  searchForm.value = {
    id: '',
    tokenId: '',
    email: '',
    validUntilStart: '',
    validUntilEnd: '',
    nextResetStart: '',
    nextResetEnd: '',
    lines: []
  }
  handleSearch()
}

const toggleSearch = () => {
  searchExpanded.value = !searchExpanded.value
  saveSearchExpandedState()
}

// Filter lines for search dropdown (uses complete server list)
const filteredSearchLines = ref([])

const filterSearchLines = (query) => {
  if (query) {
    filteredSearchLines.value = allAvailableLines.value.filter(line =>
      line.display_name.toLowerCase().includes(query.toLowerCase())
    )
  } else {
    filteredSearchLines.value = allAvailableLines.value
  }
}

// Filter lines for dialog forms (uses complete server list)
const filteredDialogLines = ref([])



// Computed property for active filters count
const activeFiltersCount = computed(() => {
  let count = 0
  if (searchForm.value.id?.trim()) count++
  if (searchForm.value.tokenId?.trim()) count++
  if (searchForm.value.email?.trim()) count++
  if (searchForm.value.validUntilStart) count++
  if (searchForm.value.validUntilEnd) count++
  if (searchForm.value.nextResetStart) count++
  if (searchForm.value.nextResetEnd) count++
  if (searchForm.value.lines?.length > 0) count++
  return count
})

// Load search expanded state from localStorage on component mount
const loadSearchExpandedState = () => {
  const saved = localStorage.getItem('subscription-search-expanded')
  if (saved !== null) {
    searchExpanded.value = JSON.parse(saved)
  }
}

// Save search expanded state to localStorage
const saveSearchExpandedState = () => {
  localStorage.setItem('subscription-search-expanded', JSON.stringify(searchExpanded.value))
}

onMounted(() => {
  loadSearchExpandedState()
  loadSubscriptions()
  loadServerList()
  // Load complete server list for search dropdown
  loadAllServersForSearch()
})
</script>

<style scoped>
.subscription-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--theme-text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

.line-count {
  cursor: pointer;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-regular) !important;
  border: 1px solid var(--theme-border-base) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.line-count:hover {
  background-color: var(--theme-fill-base) !important;
  border-color: var(--theme-primary) !important;
}

/* Dark mode line count styling */
:global(.dark) .line-count {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}

:global(.dark) .line-count:hover {
  background-color: var(--theme-fill-darker) !important;
  border-color: var(--theme-primary) !important;
}

:deep(.line-popover) {
  padding: 0;
  background-color: var(--theme-bg-primary) !important;
  border: 1px solid var(--theme-border-base) !important;
  box-shadow: var(--theme-shadow-base) !important;
}

/* Dark mode popover styling */
:global(.dark) :deep(.line-popover) {
  background-color: var(--theme-bg-tertiary) !important;
  border: 1px solid var(--theme-border-dark) !important;
  box-shadow: var(--theme-shadow-dark) !important;
}

:global(.dark) :deep(.line-popover .el-popper__arrow::before) {
  background-color: var(--theme-bg-tertiary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}

.line-list {
  width: 100%;
}

.line-list-header {
  padding: 8px 12px;
  font-weight: 600;
  border-bottom: 1px solid var(--theme-border-base);
  background-color: var(--theme-fill-light);
  color: var(--theme-text-primary);
  border-radius: 4px 4px 0 0;
}

.line-item {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--theme-border-light);
  background-color: var(--theme-bg-primary);
  transition: background-color 0.2s ease;
}

.line-item:hover {
  background-color: var(--theme-fill-extra-light);
}

.line-item:last-child {
  border-bottom: none;
}

.line-name {
  font-size: 13px;
  color: var(--theme-text-regular);
  font-weight: 500;
}

/* Clean borderless table styling matching ForwardEndpointManagement */
:deep(.el-table) {
  border-radius: 8px;
  background-color: var(--theme-bg-primary);
  border: none;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-extra-light) !important;
}

:deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

/* Dark mode specific table styling */
:global(.dark) :deep(.el-table th) {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-light) !important;
}

:global(.dark) :deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

:deep(.el-button.is-circle) {
  padding: 6px;
}

:deep(.el-card__body) {
  padding: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select) {
  width: 100%;
}

.traffic-progress {
  padding: 0 10px;
}

.traffic-progress .progress-wrapper {
  background-color: var(--theme-fill-light);
  border: 1px solid var(--theme-border-light);
  border-radius: 6px;
  height: 26px;
  width: 100%;
  position: relative;
  overflow: hidden;
  transition: var(--theme-transition);
}

.traffic-progress .progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  transition: width 0.3s ease, background 0.3s ease;
  border-radius: 5px;
  box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.traffic-progress .progress-bar.zero-progress {
  width: 0 !important;
}

.traffic-progress .progress-text {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme-text-regular);
  font-size: 12px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-weight: 500;
  white-space: nowrap;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Dark mode specific progress bar styling */
:global(.dark) .traffic-progress .progress-wrapper {
  background-color: var(--theme-fill-darker);
  border-color: var(--theme-border-dark);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

:global(.dark) .traffic-progress .progress-bar {
  background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-primary-lighter) 100%);
  box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

:global(.dark) .traffic-progress .progress-text {
  color: var(--theme-text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.el-button {
  margin: 0 2px;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.page-size-selector {
  width: 120px;
}

.pagination-controls {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
  }
}

/* Search Toggle Button Styles */
.search-toggle-btn {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
}

.search-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-toggle-text {
  margin-left: 4px;
  margin-right: 4px;
}

.filter-badge {
  margin-left: 8px;
}

.filter-badge :deep(.el-badge__content) {
  font-size: 11px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 8px;
}

/* Search Form Styles */
.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* Dark mode compatibility for search form */
:root.dark .search-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

:root.dark .search-toggle-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Token ID clickable cell styling */
.token-id-cell {
  cursor: pointer;
  color: var(--theme-primary);
  transition: background-color 0.2s ease, color 0.2s ease;
  padding: 2px 4px;
  border-radius: 4px;
  display: inline-block;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 500;
}

.token-id-cell:hover {
  background-color: var(--theme-primary-light-9);
  color: var(--theme-primary-dark-2);
}

.token-id-cell:active {
  background-color: var(--theme-primary-light-8);
}

/* Dark mode specific token ID styling */
:global(.dark) .token-id-cell {
  color: var(--theme-primary-light-3);
}

:global(.dark) .token-id-cell:hover {
  background-color: var(--theme-primary-dark-8);
  color: var(--theme-primary-light-5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsive design for search form */
@media (max-width: 768px) {
  .search-toggle-btn {
    width: 100%;
    justify-content: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .search-form .el-input,
  .search-form .el-select,
  .search-form .el-date-picker {
    width: 100% !important;
  }
}
</style>
